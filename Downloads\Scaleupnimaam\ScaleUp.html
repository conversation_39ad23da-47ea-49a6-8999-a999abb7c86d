<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScaleUp Marketing</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            overflow-x: hidden;
            background-color: #fff;
        }
        
        /* Navigation Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 80px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 60px;
            width: auto;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin: 0 20px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: #e40002; /* Primary red color */
        }

        .contact-btn {
            background-color: #e40002; /* Primary red color */
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .contact-btn:hover {
            background-color: #c40002; /* Darker red */
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }
        
        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(11, 8, 161, 0.6), rgba(11, 8, 161, 0.7)), url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80') center/cover no-repeat;
            display: flex;
            align-items: center;
            padding: 0 80px;
            position: relative;
        }
        
        .hero-content {
            max-width: 650px;
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 30px;
            color: white;
        }
        
        .hero p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .cta-button {
            background-color: #e40002; /* Primary red color */
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-button:hover {
            background-color: #c40002; /* Darker red */
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(228, 0, 2, 0.4);
        }
        
        .cta-button i {
            font-size: 18px;
        }
        
        /* Services Section */
        .services {
            padding: 100px 80px;
            background-color: #fff;
        }

        .services-container {
            display: flex;
            align-items: center;
            gap: 80px;
        }

        .stats-section {
            flex: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .stat-box {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #e40002;
            text-transform: uppercase;
            letter-spacing: 1px;
            line-height: 1.2;
        }

        .content-section {
            flex: 1;
        }

        .content-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #0b08a1;
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .content-section p {
            font-size: 1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 30px;
        }

        .contact-btn-section {
            background-color: #e40002;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-transform: uppercase;
        }

        .contact-btn-section:hover {
            background-color: #c40002;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }
        
        /* About Section */
        .about {
            padding: 100px 80px;
            background-color: #0b08a1;
            color: white;
        }

        .about-container {
            display: flex;
            gap: 80px;
            align-items: flex-start;
        }

        .about-left {
            flex: 1;
            padding-right: 40px;
        }

        .about-left h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            line-height: 1.2;
        }

        .about-right {
            flex: 1.5;
        }

        .services-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .service-card-new {
            background-color: white;
            color: #333;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .service-card-new h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .service-subtitle {
            font-size: 0.95rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .service-card-new p:last-child {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
            margin: 0;
        }

        .service-card-bottom {
            grid-column: 1 / -1;
            max-width: 50%;
        }
        
        /* Portfolio Section */
        .portfolio {
            padding: 120px 60px;
            background-color: #fff;
        }

        .portfolio-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .portfolio-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #e40002;
            margin-bottom: 10px;
        }

        .portfolio-header p {
            font-size: 1.1rem;
            color: #0b08a1;
            font-weight: 600;
        }

        .portfolio-categories {
            display: grid;
            grid-template-columns: 1fr 1fr;
            margin-bottom: 40px;
            margin-left: -60px;
            margin-right: -60px;
        }

        .category-header {
            text-align: center;
            padding: 20px;
        }

        .category-header:first-child {
            background-color: #333;
            color: white;
        }

        .category-header:last-child {
            background-color: #f5f5f5;
            color: #333;
        }

        .category-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 50px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .portfolio-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .portfolio-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .portfolio-image {
            width: 100%;
            height: 300px;
            overflow: hidden;
            position: relative;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .portfolio-item:hover .portfolio-image img {
            transform: scale(1.05);
        }

        .portfolio-date {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 700;
            color: #333;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .portfolio-more {
            text-align: center;
        }

        .more-btn {
            background-color: #e40002;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-transform: uppercase;
        }

        .more-btn:hover {
            background-color: #c40002;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }

        /* Portfolio Responsive Design */
        @media (max-width: 1024px) {
            .portfolio-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .portfolio {
                padding: 80px 40px;
            }

            .portfolio-categories {
                margin-left: -40px;
                margin-right: -40px;
            }
        }

        @media (max-width: 768px) {
            .portfolio-grid {
                grid-template-columns: 1fr;
                gap: 25px;
            }

            .portfolio {
                padding: 60px 20px;
            }

            .portfolio-categories {
                margin-left: -20px;
                margin-right: -20px;
            }

            .portfolio-image {
                height: 250px;
            }

            .category-header {
                padding: 15px;
            }

            .category-header h3 {
                font-size: 1rem;
            }
        }
        
        /* Contact Section */
        .contact {
            padding: 100px 80px;
            background-color: #f8f9fa;
        }
        
        .contact-container {
            display: flex;
            gap: 50px;
        }
        
        .contact-form {
            flex: 1;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }
        
        .contact-form h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-size: 1rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #e40002; /* Primary red color */
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-btn {
            background-color: #e40002; /* Primary red color */
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            background-color: #c40002; /* Darker red */
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(228, 0, 2, 0.4);
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-info h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .contact-info p {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 30px;
        }
        
        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .contact-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(228, 0, 2, 0.1); /* Light red background */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e40002; /* Primary red color */
            font-size: 1.2rem;
        }
        
        .contact-text h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .contact-text p {
            font-size: 1rem;
            color: #666;
            margin: 0;
        }
        
        /* Footer */
        footer {
            background-color: #333;
            color: #fff;
            padding: 60px 80px 30px;
        }
        
        .footer-content {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .footer-logo {
            margin-bottom: 20px;
        }

        .footer-logo img {
            height: 35px;
            width: auto;
        }
        
        .footer-about p {
            color: #ccc;
            line-height: 1.6;
            max-width: 300px;
        }
        
        .footer-links h4 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .footer-links ul {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #e40002; /* Primary red color */
        }
        
        .footer-contact h4 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .footer-contact p {
            color: #ccc;
            margin-bottom: 10px;
        }
        
        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ccc;
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            background-color: #e40002; /* Primary red color */
            color: white;
        }
        
        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #ccc;
            font-size: 0.9rem;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .navbar {
                padding: 15px 40px;
            }
            
            .hero {
                padding: 0 40px;
            }
            
            .hero h1 {
                font-size: 2.8rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 80px 40px;
            }
            
            .about-content {
                flex-direction: column;
            }
            
            .contact-container {
                flex-direction: column;
            }
        }
        
        @media (max-width: 768px) {
            .navbar {
                padding: 15px 20px;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero {
                padding: 0 20px;
                text-align: center;
            }
            
            .hero-content {
                max-width: 100%;
            }
            
            .hero h1 {
                font-size: 2.2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 60px 20px;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .about-text h2 {
                font-size: 2rem;
            }
            
            .stats {
                flex-direction: column;
            }
            
            .footer-content {
                flex-direction: column;
                gap: 30px;
            }
        }
        
        @media (max-width: 576px) {
            .hero h1 {
                font-size: 1.8rem;
            }
            
            .hero p {
                font-size: 0.9rem;
            }
            
            .cta-button {
                padding: 14px 24px;
                font-size: 16px;
            }
            
            .section-title h2 {
                font-size: 1.8rem;
            }
            
            .about-text h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="logo">
            <img src="Pictures/logo.jpg" alt="ScaleUp Marketing">
        </div>
        <ul class="nav-links">
            <li><a href="#home">HOME</a></li>
            <li><a href="#about">ABOUT US</a></li>
            <li><a href="#services">SERVICES</a></li>
            <li><a href="#testimonials">PORTFOLIO</a></li>
        </ul>
        <button class="contact-btn">CONTACT</button>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>ScaleUp Your Growth, Elevate Your Success</h1>
            <p>We provide sophisticated, data-driven strategies to enhance your online presence, drive targeted traffic, and generate high-quality leads. Whether you're an emerging startup or an established enterprise, our expertise guarantees measurable growth and long-lasting success.</p>
            <button class="cta-button">
                GET IN TOUCH
                <i class="fas fa-arrow-right"></i>
            </button>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="services-container">
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-number">200+</div>
                        <div class="stat-label">CLIENTS</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">8</div>
                        <div class="stat-label">DIFFERENT<br>COUNTRIES</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">CUSTOM WEBSITES</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number">80,000+</div>
                        <div class="stat-label">WORKING HOURS</div>
                    </div>
                </div>
            </div>
            <div class="content-section">
                <h2>Let's Scale Together! Our Strategies, Your Success</h2>
                <p>ScaleUp Marketing provides strategic digital marketing solutions that fuel business growth. Our experienced team is committed to developing customized strategies that enhance brand visibility, effectively engage audiences, and maximize return on investment. With a deep understanding of industry trends and access to cutting-edge tools, we empower businesses to maintain a competitive edge in the constantly evolving digital landscape.</p>
                <button class="contact-btn-section">CONTACT</button>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="about-container">
            <div class="about-left">
                <h2>ScaleUp your online presence, Grow your sales with our expert strategies.</h2>
            </div>
            <div class="about-right">
                <div class="services-cards">
                    <div class="service-card-new">
                        <h3>Website Design & Development</h3>
                        <p class="service-subtitle">Your website acts as a digital gateway to your brand.</p>
                        <p>We create visually appealing, user-friendly, and fully responsive websites that offer seamless navigation and an outstanding user experience, ensuring maximum engagement and conversions.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>PPC (Pay-Per-Click)</h3>
                        <p class="service-subtitle">Enhance your advertising budget with precisely targeted PPC campaigns.</p>
                        <p>We develop and manage Google Ads and social media advertising strategies aimed at attracting high-intent traffic, ensuring effective lead generation and cost efficiency.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>Social Media Marketing</h3>
                        <p class="service-subtitle">Build a strong presence on social media through effective campaigns.</p>
                        <p>We utilize data-driven strategies to enhance brand engagement, grow your audience, and create meaningful interactions via engaging content and targeted advertising.</p>
                    </div>
                    <div class="service-card-new">
                        <h3>SEO (Search Engine Optimization)</h3>
                        <p class="service-subtitle">Enhance your search engine rankings and broaden your online presence.</p>
                        <p>Our SEO experts use best practices to optimize your website, improve keyword relevance, and enrich content, ultimately boosting organic traffic and visibility.</p>
                    </div>
                    <div class="service-card-new service-card-bottom">
                        <h3>Email Marketing</h3>
                        <p class="service-subtitle">Strengthen customer relationships and increase conversions through effective email campaigns.</p>
                        <p>Our approach includes personalized messaging, automation, and targeted content delivery to engage your audience effectively and drive measurable results.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="portfolio" id="portfolio">
        <div class="portfolio-header">
            <h2>Portfolio</h2>
            <p>Explore Our Work</p>
        </div>

        <div class="portfolio-categories">
            <div class="category-header">
                <h3>Web Design & Development</h3>
            </div>
            <div class="category-header">
                <h3>Digital Marketing</h3>
            </div>
        </div>

        <div class="portfolio-grid">
            <!-- Web Design & Development Projects -->
            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 1">
                </div>
                <div class="portfolio-date">AUG / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 2">
                </div>
                <div class="portfolio-date">AUG / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 3">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 4">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1555421689-491a97ff2040?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 5">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 6">
                </div>
                <div class="portfolio-date">MAY / 2024</div>
            </div>

            <!-- Row 3 -->
            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 7">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 8">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 9">
                </div>
                <div class="portfolio-date">APR / 2024</div>
            </div>

            <!-- Row 4 -->
            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1553028826-f4804a6dba3b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 10">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 11">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 12">
                </div>
                <div class="portfolio-date">MAR / 2024</div>
            </div>

            <!-- Row 5 -->
            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 13">
                </div>
                <div class="portfolio-date">FEB / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 14">
                </div>
                <div class="portfolio-date">FEB / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 15">
                </div>
                <div class="portfolio-date">FEB / 2024</div>
            </div>

            <!-- Row 6 -->
            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 16">
                </div>
                <div class="portfolio-date">JAN / 2024</div>
            </div>

            <div class="portfolio-item web-design">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1504639725590-34d0984388bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 17">
                </div>
                <div class="portfolio-date">JAN / 2024</div>
            </div>

            <div class="portfolio-item digital-marketing">
                <div class="portfolio-image">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" alt="Project 18">
                </div>
                <div class="portfolio-date">JAN / 2024</div>
            </div>
        </div>

        <div class="portfolio-more">
            <button class="more-btn">More</button>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="section-title">
            <h2>Get In Touch</h2>
            <p>Ready to scale your business? Contact us today for a free consultation</p>
        </div>
        <div class="contact-container">
            <div class="contact-form">
                <h3>Send Us a Message</h3>
                <form>
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" placeholder="John Doe">
                    </div>
                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" placeholder="How can we help?">
                    </div>
                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" placeholder="Tell us about your project..."></textarea>
                    </div>
                    <button type="submit" class="submit-btn">Send Message</button>
                </form>
            </div>
            <div class="contact-info">
                <h3>Contact Information</h3>
                <p>Reach out to us using any of the methods below. Our team is ready to help you grow your business.</p>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Our Location</h4>
                            <p>123 Marketing Street, Suite 100, New York, NY 10001</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Phone Number</h4>
                            <p>+****************</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Email Address</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-about">
                <div class="footer-logo">
                    <img src="https://via.placeholder.com/120x35/e63946/ffffff?text=ScaleUp" alt="ScaleUp Marketing">
                </div>
                <p>We are a data-driven marketing agency focused on delivering measurable results and sustainable growth for our clients.</p>
                <div class="social-icons">
                    <div class="social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-twitter"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-linkedin-in"></i>
                    </div>
                </div>
            </div>
            <div class="footer-links">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About Us</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#testimonials">Case Studies</a></li>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>
            <div class="footer-links">
                <h4>Our Services</h4>
                <ul>
                    <li><a href="#">SEO Optimization</a></li>
                    <li><a href="#">Social Media Marketing</a></li>
                    <li><a href="#">Email Marketing</a></li>
                    <li><a href="#">Content Marketing</a></li>
                    <li><a href="#">PPC Advertising</a></li>
                    <li><a href="#">Analytics & Reporting</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h4>Contact Us</h4>
                <p>123 Marketing Street, Suite 100</p>
                <p>New York, NY 10001</p>
                <p>+****************</p>
                <p><EMAIL></p>
            </div>
        </div>
        <div class="copyright">
            <p>&copy; 2023 ScaleUp Marketing. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.padding = '15px 80px';
                navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.padding = '20px 80px';
                navbar.style.backgroundColor = '#fff';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form submission
        document.querySelector('.contact-form form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We will get back to you soon.');
            this.reset();
        });

        // Counter Animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16); // 60fps
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target.toString().includes('.') ? target.toFixed(1) : Math.floor(target);
                    if (target >= 1000) {
                        element.textContent = element.textContent.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                    }
                    // Add the + sign back if it was in the original
                    if (element.dataset.original && element.dataset.original.includes('+')) {
                        element.textContent += '+';
                    }
                    clearInterval(timer);
                } else {
                    let currentValue = Math.floor(start);
                    if (currentValue >= 1000) {
                        element.textContent = currentValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                    } else {
                        element.textContent = currentValue;
                    }
                    // Add the + sign for display during animation if original had it
                    if (element.dataset.original && element.dataset.original.includes('+')) {
                        element.textContent += '+';
                    }
                }
            }, 16);
        }

        // Intersection Observer for counter animation
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        if (!stat.classList.contains('animated')) {
                            stat.classList.add('animated');
                            // Store original text
                            stat.dataset.original = stat.textContent;
                            // Extract number from text (remove + and commas)
                            const targetValue = parseInt(stat.textContent.replace(/[+,]/g, ''));
                            // Start animation
                            animateCounter(stat, targetValue);
                        }
                    });
                }
            });
        }, observerOptions);

        // Observe the services section
        const servicesSection = document.querySelector('.services');
        if (servicesSection) {
            observer.observe(servicesSection);
        }

        // Portfolio item click functionality
        document.addEventListener('DOMContentLoaded', function() {
            const portfolioItems = document.querySelectorAll('.portfolio-item');

            portfolioItems.forEach(function(item, index) {
                item.addEventListener('click', function() {
                    const projectNumber = index + 1;
                    const category = item.classList.contains('web-design') ? 'Web Design & Development' : 'Digital Marketing';
                    const date = item.querySelector('.portfolio-date').textContent;

                    // Create modal or alert with project details
                    const projectDetails = `
Project ${projectNumber}
Category: ${category}
Date: ${date}

This is a sample project description. In a real implementation, you would:
- Show a detailed project gallery
- Display project description and objectives
- List technologies used
- Show client testimonials
- Include project results and metrics
                    `;

                    alert(projectDetails);

                    // Alternative: You could redirect to a detailed project page
                    // window.location.href = `/portfolio/project-${projectNumber}`;
                });
            });
        });
    </script>
</body>
</html>