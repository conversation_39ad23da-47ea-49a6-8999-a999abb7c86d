<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScaleUp Marketing</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            overflow-x: hidden;
            background-color: #fff;
        }
        
        /* Navigation Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 80px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #e63946; /* Primary red color */
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin: 0 20px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: #e63946; /* Primary red color */
        }
        
        .contact-btn {
            background-color: #e63946; /* Primary red color */
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .contact-btn:hover {
            background-color: #d62828; /* Darker red */
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(230, 57, 70, 0.4);
        }
        
        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80') center/cover no-repeat;
            display: flex;
            align-items: center;
            padding: 0 80px;
            position: relative;
        }
        
        .hero-content {
            max-width: 650px;
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 30px;
            color: white;
        }
        
        .hero p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .cta-button {
            background-color: #e63946; /* Primary red color */
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .cta-button:hover {
            background-color: #d62828; /* Darker red */
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(230, 57, 70, 0.4);
        }
        
        .cta-button i {
            font-size: 18px;
        }
        
        /* Services Section */
        .services {
            padding: 100px 80px;
            background-color: #fff;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .section-title p {
            font-size: 1.1rem;
            color: #666;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .service-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 40px 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-top: 4px solid #e63946; /* Primary red accent */
        }
        
        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
        }
        
        .service-icon {
            font-size: 2.5rem;
            color: #e63946; /* Primary red color */
            margin-bottom: 20px;
        }
        
        .service-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .service-card p {
            color: #666;
            line-height: 1.6;
        }
        
        /* About Section */
        .about {
            padding: 100px 80px;
            background-color: #f8f9fa;
        }
        
        .about-content {
            display: flex;
            align-items: center;
            gap: 50px;
        }
        
        .about-text {
            flex: 1;
        }
        
        .about-text h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 30px;
        }
        
        .about-text p {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .about-image {
            flex: 1;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .about-image img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .stats {
            display: flex;
            gap: 30px;
            margin-top: 40px;
        }
        
        .stat-item {
            flex: 1;
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #e63946; /* Primary red color */
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #666;
        }
        
        /* Testimonials Section */
        .testimonials {
            padding: 100px 80px;
            background-color: #fff;
        }
        
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .testimonial-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            position: relative;
        }
        
        .testimonial-card::before {
            content: '\f10d';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 2rem;
            color: rgba(230, 57, 70, 0.1); /* Light red */
        }
        
        .testimonial-text {
            font-size: 1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
        }
        
        .author-avatar img {
            width: 100%;
            height: auto;
        }
        
        .author-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .author-info p {
            font-size: 0.9rem;
            color: #666;
        }
        
        /* Contact Section */
        .contact {
            padding: 100px 80px;
            background-color: #f8f9fa;
        }
        
        .contact-container {
            display: flex;
            gap: 50px;
        }
        
        .contact-form {
            flex: 1;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }
        
        .contact-form h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-size: 1rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #e63946; /* Primary red color */
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-btn {
            background-color: #e63946; /* Primary red color */
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            background-color: #d62828; /* Darker red */
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(230, 57, 70, 0.4);
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-info h3 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .contact-info p {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 30px;
        }
        
        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .contact-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(230, 57, 70, 0.1); /* Light red background */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e63946; /* Primary red color */
            font-size: 1.2rem;
        }
        
        .contact-text h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .contact-text p {
            font-size: 1rem;
            color: #666;
            margin: 0;
        }
        
        /* Footer */
        footer {
            background-color: #333;
            color: #fff;
            padding: 60px 80px 30px;
        }
        
        .footer-content {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .footer-logo {
            font-size: 24px;
            font-weight: 700;
            color: #e63946; /* Primary red color */
            margin-bottom: 20px;
        }
        
        .footer-about p {
            color: #ccc;
            line-height: 1.6;
            max-width: 300px;
        }
        
        .footer-links h4 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .footer-links ul {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #e63946; /* Primary red color */
        }
        
        .footer-contact h4 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .footer-contact p {
            color: #ccc;
            margin-bottom: 10px;
        }
        
        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ccc;
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            background-color: #e63946; /* Primary red color */
            color: white;
        }
        
        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #ccc;
            font-size: 0.9rem;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .navbar {
                padding: 15px 40px;
            }
            
            .hero {
                padding: 0 40px;
            }
            
            .hero h1 {
                font-size: 2.8rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 80px 40px;
            }
            
            .about-content {
                flex-direction: column;
            }
            
            .contact-container {
                flex-direction: column;
            }
        }
        
        @media (max-width: 768px) {
            .navbar {
                padding: 15px 20px;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero {
                padding: 0 20px;
                text-align: center;
            }
            
            .hero-content {
                max-width: 100%;
            }
            
            .hero h1 {
                font-size: 2.2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .services, .about, .testimonials, .contact {
                padding: 60px 20px;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .about-text h2 {
                font-size: 2rem;
            }
            
            .stats {
                flex-direction: column;
            }
            
            .footer-content {
                flex-direction: column;
                gap: 30px;
            }
        }
        
        @media (max-width: 576px) {
            .hero h1 {
                font-size: 1.8rem;
            }
            
            .hero p {
                font-size: 0.9rem;
            }
            
            .cta-button {
                padding: 14px 24px;
                font-size: 16px;
            }
            
            .section-title h2 {
                font-size: 1.8rem;
            }
            
            .about-text h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="logo">ScaleUp</div>
        <ul class="nav-links">
            <li><a href="#home">HOME</a></li>
            <li><a href="#about">ABOUT US</a></li>
            <li><a href="#services">SERVICES</a></li>
            <li><a href="#testimonials">CASE STUDIES</a></li>
        </ul>
        <button class="contact-btn">CONTACT</button>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>ScaleUp Your Growth, Elevate Your Success</h1>
            <p>We provide data-driven strategies to enhance your online presence, attract targeted traffic, and generate high-quality leads. Serving both startups and established businesses, we guarantee measurable growth and sustainable success.</p>
            <button class="cta-button">
                GET IN TOUCH
                <i class="fas fa-arrow-right"></i>
            </button>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="section-title">
            <h2>Our Services</h2>
            <p>We offer a comprehensive range of digital marketing services tailored to your business needs</p>
        </div>
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>SEO Optimization</h3>
                <p>Improve your search engine rankings and drive organic traffic with our proven SEO strategies.</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <h3>Social Media Marketing</h3>
                <p>Engage your audience and build brand awareness through strategic social media campaigns.</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h3>Email Marketing</h3>
                <p>Nurture leads and convert prospects into customers with targeted email campaigns.</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Content Marketing</h3>
                <p>Create valuable content that attracts and retains your target audience.</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-ad"></i>
                </div>
                <h3>PPC Advertising</h3>
                <p>Drive immediate results with targeted pay-per-click advertising campaigns.</p>
            </div>
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-analytics"></i>
                </div>
                <h3>Analytics & Reporting</h3>
                <p>Track your performance with detailed analytics and actionable insights.</p>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="about-content">
            <div class="about-text">
                <h2>About ScaleUp Marketing</h2>
                <p>We are a team of passionate digital marketers dedicated to helping businesses grow and succeed in the online world. With years of experience and a data-driven approach, we deliver measurable results that exceed expectations.</p>
                <p>Our mission is to provide innovative marketing solutions that drive growth, increase brand visibility, and maximize ROI for our clients. We believe in transparency, collaboration, and continuous improvement.</p>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Happy Clients</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">300+</div>
                        <div class="stat-label">Projects Completed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">Client Satisfaction</div>
                    </div>
                </div>
            </div>
            <div class="about-image">
                <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80" alt="Our Team">
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials" id="testimonials">
        <div class="section-title">
            <h2>Client Testimonials</h2>
            <p>Don't just take our word for it - hear from our satisfied clients</p>
        </div>
        <div class="testimonials-grid">
            <div class="testimonial-card">
                <p class="testimonial-text">"ScaleUp Marketing transformed our online presence completely. Their data-driven approach helped us increase our leads by 150% in just three months. Highly recommended!"</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Client">
                    </div>
                    <div class="author-info">
                        <h4>Michael Johnson</h4>
                        <p>CEO, TechStart Inc.</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <p class="testimonial-text">"The team at ScaleUp Marketing is exceptional. They understood our business needs and delivered results beyond our expectations. Our ROI has never been better!"</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Client">
                    </div>
                    <div class="author-info">
                        <h4>Sarah Williams</h4>
                        <p>Marketing Director, Fashion Forward</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <p class="testimonial-text">"Working with ScaleUp Marketing has been a game-changer for our business. Their strategic approach and attention to detail helped us dominate our market."</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Client">
                    </div>
                    <div class="author-info">
                        <h4>David Chen</h4>
                        <p>Founder, Growth Solutions</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="section-title">
            <h2>Get In Touch</h2>
            <p>Ready to scale your business? Contact us today for a free consultation</p>
        </div>
        <div class="contact-container">
            <div class="contact-form">
                <h3>Send Us a Message</h3>
                <form>
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" placeholder="John Doe">
                    </div>
                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" placeholder="How can we help?">
                    </div>
                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" placeholder="Tell us about your project..."></textarea>
                    </div>
                    <button type="submit" class="submit-btn">Send Message</button>
                </form>
            </div>
            <div class="contact-info">
                <h3>Contact Information</h3>
                <p>Reach out to us using any of the methods below. Our team is ready to help you grow your business.</p>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Our Location</h4>
                            <p>123 Marketing Street, Suite 100, New York, NY 10001</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Phone Number</h4>
                            <p>+****************</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-text">
                            <h4>Email Address</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-about">
                <div class="footer-logo">ScaleUp</div>
                <p>We are a data-driven marketing agency focused on delivering measurable results and sustainable growth for our clients.</p>
                <div class="social-icons">
                    <div class="social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-twitter"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="social-icon">
                        <i class="fab fa-linkedin-in"></i>
                    </div>
                </div>
            </div>
            <div class="footer-links">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About Us</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#testimonials">Case Studies</a></li>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>
            <div class="footer-links">
                <h4>Our Services</h4>
                <ul>
                    <li><a href="#">SEO Optimization</a></li>
                    <li><a href="#">Social Media Marketing</a></li>
                    <li><a href="#">Email Marketing</a></li>
                    <li><a href="#">Content Marketing</a></li>
                    <li><a href="#">PPC Advertising</a></li>
                    <li><a href="#">Analytics & Reporting</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h4>Contact Us</h4>
                <p>123 Marketing Street, Suite 100</p>
                <p>New York, NY 10001</p>
                <p>+****************</p>
                <p><EMAIL></p>
            </div>
        </div>
        <div class="copyright">
            <p>&copy; 2023 ScaleUp Marketing. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.padding = '15px 80px';
                navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.padding = '20px 80px';
                navbar.style.backgroundColor = '#fff';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form submission
        document.querySelector('.contact-form form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We will get back to you soon.');
            this.reset();
        });
    </script>
</body>
</html>